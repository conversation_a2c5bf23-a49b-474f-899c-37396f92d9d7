import cv2
import yaml
import torch
import numpy as np
import random

import torch.nn.functional as F


TEXT_SCALE = 1.0
TEXT_THICKNESS = 2
FONT = cv2.FONT_HERSHEY_SIMPLEX


g = torch.Generator()
g.manual_seed(42)


def seed_worker(worker_id):
    """ Seed worker for DataLoader with 'g' """
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    random.seed(worker_seed)


def normalize(vector: np.ndarray) -> np.ndarray:
    """Normalize a vector to unit length."""
    return vector / np.linalg.norm(vector)


def read_congfig_file(config_file_path: str):
    """
    Load config file.

    :param config_file_path: base path of data
    :return: data in config file
    """
    with open(config_file_path, 'r') as file:
        data = yaml.safe_load(file)
    return data


def gaze_vector_to_pitchyaw(gaze_vector: np.ndarray) -> np.ndarray:
    """
    Convert a 3D gaze vector to pitch and yaw angles in radians.

    Args:
        gaze_vector: 3D gaze direction vector as numpy array [x, y, z]

    Returns:
        - pitch: vertical angle in radians
        - yaw: horizontal angle in radians
    """
    x = gaze_vector[0]
    y = gaze_vector[1]
    z = gaze_vector[2]
    
    pitch = np.arcsin(-y)
    yaw = np.arctan2(-x, -z)
    
    return pitch, yaw


def pitchyaw_to_3d_vector(pitchyaw: torch.Tensor) -> torch.Tensor:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors.

    Args:
        pitchyaw: Tensor of shape (N, 2), where [:, 0] = pitch and [:, 1] = yaw (in radians)

    Returns:
        Tensor of shape (N, 3) containing 3D unit direction vectors
    """
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -torch.cos(pitch) * torch.sin(yaw)
    y = -torch.sin(pitch)
    z = -torch.cos(pitch) * torch.cos(yaw)

    return torch.stack([x, y, z], dim=1)


def pitchyaw_to_3d_vector_numpy(pitchyaw: np.ndarray) -> np.ndarray:
    """
    Convert 2D pitch and yaw angles to 3D unit direction vectors (NumPy version).

    Args:
        pitchyaw: Array of shape (N, 2), where [:, 0] = pitch and [:, 1] = yaw (in radians)

    Returns:
        Array of shape (N, 3) containing 3D unit direction vectors
    """
    pitchyaw = np.asarray(pitchyaw)
    
    if pitchyaw.ndim == 1:
        pitchyaw = pitchyaw.reshape(1, 2)  # Convert (2,) ➝ (1, 2)
    
    pitch = pitchyaw[:, 0]
    yaw = pitchyaw[:, 1]

    x = -np.cos(pitch) * np.sin(yaw)
    y = -np.sin(pitch)
    z = -np.cos(pitch) * np.cos(yaw)

    return np.stack([x, y, z], axis=1).squeeze(0)


def calculate_gaze_angle_error(labels: torch.Tensor,
                               outputs: torch.Tensor,
                               torch_inv_matrix: np.ndarray = None,
                               eps: float = 1e-6) -> tuple[torch.Tensor, torch.Tensor]:
    """
    Calculate the angular error between predicted and ground truth gaze directions in radians and degrees.

    Args:
        labels: Ground truth gaze angles (pitch, yaw), shape (N, 2)
        outputs: Predicted gaze angles (pitch, yaw), shape (N, 2)
        eps: Small constant to prevent NaNs in arccos

    Returns:
        Tuple of mean angular error in radians and degrees
    """
    # Convert to 3D vectors
    labels = pitchyaw_to_3d_vector(labels)
    outputs = pitchyaw_to_3d_vector(outputs)

    if torch_inv_matrix is not None:
        labels = torch.matmul(torch_inv_matrix, labels.T).T
        outputs = torch.matmul(torch_inv_matrix, outputs.T).T
        
    # Normalize 3D vectors
    labels_norm = labels / torch.linalg.norm(labels, dim=1, keepdim=True).clamp(min=1e-6)
    outputs_norm = outputs / torch.linalg.norm(outputs, dim=1, keepdim=True).clamp(min=1e-6)
    
    # Cosine similarity and clamp
    cos_sim = F.cosine_similarity(outputs_norm, labels_norm, dim=1)
    cos_sim = cos_sim.clamp(-1.0 + eps, 1.0 - eps)

    # Angular error
    rad_error = torch.arccos(cos_sim)
    deg_error = torch.rad2deg(rad_error)
    
    # Mean error
    mean_rad_error = rad_error.mean().item()
    mean_deg_error = deg_error.mean().item()

    return mean_rad_error, mean_deg_error


def clahe(rgb_img: np.ndarray):
    lab = cv2.cvtColor(rgb_img, cv2.COLOR_RGB2Lab)

    # Split LAB channels
    L, A, B = cv2.split(lab)

    # Create CLAHE object
    # clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(2, 2))

    # Apply CLAHE to L channel
    L_clahe = clahe.apply(L)

    # Merge and convert back to BGR
    lab_clahe = cv2.merge((L_clahe, A, B))
    result = cv2.cvtColor(lab_clahe, cv2.COLOR_Lab2RGB)
    
    return result


def is_almost_same(A, B, fro_tol=1e-3, angle_tol=1e-2, as_vector=False):
    """
    Check if two matrices or vectors are almost the same.
    
    Parameters
    ----------
    A, B : np.ndarray
        Inputs to compare (same shape).
    atol : float
        Absolute tolerance (numerical noise).
    rtol : float
        Relative tolerance.
    as_vector : bool
        If True, treat A and B as direction vectors and compare by angle.
        
    Returns
    -------
    result : dict
        {
          "fro_error": float (for matrices),
          "l2_error": float (for vectors),
          "angle_error_deg": float,
          "almost_same": bool
        }
    """
    A = np.array(A, dtype=np.float64)
    B = np.array(B, dtype=np.float64)

    if as_vector:
        # Normalize both (gaze directions)
        A /= (np.linalg.norm(A) + 1e-12)
        B /= (np.linalg.norm(B) + 1e-12)
        
        l2_err = np.linalg.norm(A - B)
        dot = np.clip(np.dot(A, B), -1.0, 1.0)
        angle_err = np.degrees(np.arccos(dot))
        
        assert l2_err < fro_tol or angle_err < angle_tol, \
            f"Mismatch: L2={l2_err:.6f}, Angle={angle_err:.3f}°"
            
        return {
            "l2_error": float(l2_err),
            "angle_error_deg": float(angle_err),
        }
        
    else:
        # Matrix case
        fro_err = np.linalg.norm(A - B, ord='fro')
        angle_err = np.degrees(
            np.arccos(np.clip((np.trace(A.T @ B) - 1) / 2, -1.0, 1.0))
        )
        assert fro_err < fro_tol or angle_err < angle_tol, \
            f"Mismatch: Frobenius={fro_err:.6f}, Angle={angle_err:.3f}°"
            
        return {
            "fro_error": float(fro_err),
            "angle_error_deg": float(angle_err)
        }


def draw_gaze_arrow_from_vector(img, vector, color=(0, 255, 0), thickness=2, length=50):
    """
    Draw gaze arrow on an image based on pitch and yaw angles.

    Args:
        img: Input image
        pitch: Pitch angle in radians
        yaw: Yaw angle in radians
        color: Arrow color in BGR format (default: red)
        thickness: Arrow thickness
        length: Arrow length in pixels

    Returns:
        Image with drawn arrow
    """
    h, w = img.shape[:2]
    center = (w // 2, h // 2)

    # Get 3D gaze vector (x, y components for 2D projection)
    x, y, _ = vector

    # Project to 2D (image plane), ignoring Z
    dx = x * length
    dy = y * length

    # Calculate end point
    end_point = (int(center[0] + dx), int(center[1] + dy))

    # Draw arrow
    cv2.arrowedLine(img, center, end_point, color, thickness, tipLength=0.3)

    return img


def draw_pitch_yaw_from_vector(img, vector, label="PR"):
    """
    Draw pitch and yaw angles from a 3D gaze vector on an image.

    Args:
        img: Input image
        vector: 3D gaze direction vector as numpy array [x, y, z]
        label: Label to display (default: "PR")

    Returns:
        Image with drawn label
    """
    h, w, _ = img.shape
    
    shift = h//3 if label == "GT" else 0
    color = (0, 255, 0) if label == "GT" else (0, 100, 255)
    pitch, yaw = gaze_vector_to_pitchyaw(vector)
    pitch_deg, yaw_deg = np.rad2deg(pitch), np.rad2deg(yaw)
    label_pitch, label_yaw = f"P: {pitch_deg:.0f}", f"Y: {yaw_deg:.0f}"
    cv2.putText(img, label_pitch, (1, 10+shift), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
    cv2.putText(img, label_yaw, (1, 20+shift), cv2.FONT_HERSHEY_SIMPLEX, 0.3, color, 1)
    
    return img


def add_lable(canvas, org, label, font, text_scale, text_thickness, color=(255, 255, 255), center=None, deg_symb=None):
    # Remove (Inv) from label
    text = label.replace(" (Inv)", "") if "Inv" in label else label
    
    # Get text size
    (tw, th), baseline = cv2.getTextSize(text, font, text_scale, text_thickness)
    
    # Center text
    if center:
        org = (int(org[0] - tw / 2), int(org[1] + th / 2))  # center text box on pos
    
    # Degree symbol
    deg_symb = (int(org[0] + tw) + baseline // 2, int(org[1] - th))
    
    # Write text
    cv2.putText(
        canvas,
        label,
        org,
        font,
        text_scale,
        color,
        text_thickness
    )
    # Draw degree symbol
    cv2.circle(canvas, deg_symb, 4, color, 2)
    return org, deg_symb
    

def draw_difference_between_gaze(canvas, gt_px, pred_px, angular_error, inv_angular_error):
    """
    Draw the difference between the ground truth and predicted gaze on an image.

    Args:
        canvas: Input image
        gt_px: Ground truth pixel coordinates (x, y)
        pred_px: Predicted pixel coordinates (x, y)
        angular_error: Angular error in degrees
        inv_angular_error: Inversed angular error in degrees
        color: Arrow color in BGR format (default: red)
        thickness: Arrow thickness
        length: Arrow length in pixels

    Returns:
        Image with drawn arrow
    """
    org = (50, 50)
    label = f"Angular Error: {angular_error:.1f}"
    add_lable(canvas, org, label, FONT, TEXT_SCALE, TEXT_THICKNESS, color=(255, 255, 255), center=None, deg_symb=True)
    
    org = (50, 100)
    label = f"Angular Error: {inv_angular_error:.1f} (Inv)"
    add_lable(canvas, org, label, FONT, TEXT_SCALE, TEXT_THICKNESS, color=(255, 255, 255), center=None, deg_symb=True)
    
    if pred_px is not None:
        # Show degree on line
        offset = 40
        p1 = np.asarray(gt_px, dtype=float)
        p2 = np.asarray(pred_px, dtype=float)

        # midpoint of the line
        mid = (p1 + p2) / 2.0

        # direction vector and its perpendicular (normalize to unit)
        d = p2 - p1
        norm = np.hypot(d[0], d[1])
        if norm < 1e-9:
            # degenerate line; just place text at p1 with small shift
            d_perp = np.array([0.0, -1.0])
        else:
            d /= norm
            d_perp = np.array([-d[1], d[0]])   # rotate 90° CCW

        # shifted position along perpendicular
        org = tuple((mid + d_perp * offset).astype(int))
        label = f"{inv_angular_error:.1f}"
        add_lable(canvas, org, label, FONT, TEXT_SCALE, TEXT_THICKNESS, color=(255, 255, 255), center=True, deg_symb=True)
        
        # Draw line between gt and pred
        cv2.line(canvas, gt_px, pred_px, (255, 255, 255), 2)    

        # Draw the predicted point on screen
        cv2.circle(canvas, pred_px, 20, (0, 100, 255), -1)
    
    # Draw the ground truth point on screen
    cv2.circle(canvas, gt_px, 20, (0, 255, 0), -1)
        
    return canvas


def create_screen_canvas(screen):
    # Box settings
    box_color = (0, 255, 0)     # Green
    thickness = 2
    rows, cols = 6, 8
    box_height = screen.height // rows
    box_width = screen.width // cols

    # Font settings
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 1.0
    font_color = (255, 255, 255)    # Black
    font_thickness = 2

    # Create a black canvas
    screen_canvas = np.zeros((screen.height, screen.width, 3), dtype=np.uint8) * 255

    # Draw the screen grids
    box_number = 0
    for row in range(rows):
        for col in range(cols):
            box_number += 1

            # Get label name
            label = f"Box: {box_number}"

            # Get box coordinates
            top_left = (col * box_width, row * box_height)
            bottom_right = ((col + 1) * box_width, (row + 1) * box_height)

            # Get label coordinates placed in the box center
            (text_width, text_height), _ = cv2.getTextSize(label, font, font_scale, font_thickness)
            text_x = top_left[0] + (box_width - text_width) // 2
            text_y = top_left[1] + (box_height + text_height) // 2

            # Draw canvas
            cv2.rectangle(screen_canvas, top_left, bottom_right, box_color, thickness)
            cv2.putText(screen_canvas, label, (text_x, text_y), font, font_scale, font_color, font_thickness)
            
    # Initialize the heatmap
    heatmap = np.zeros((screen.height, screen.width), dtype=np.float32)
    
    return screen_canvas, heatmap
