from dataclasses import dataclass
from typing import Dict, Tu<PERSON>, Optional, Any
import numpy as np
import cv2
import logging
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class Point3D:
    """Represents a point in 3D space"""

    x: float
    y: float
    z: float


@dataclass
class Plane3D:
    """Represents a plane equation ax + by + cz + d = 0"""

    a: float
    b: float
    c: float
    d: float


@dataclass
class TVCorners:
    """Represents the four corners of a TV in 3D space"""

    top_left: np.ndarray  # shape: (3,)
    top_right: np.ndarray  # shape: (3,)
    bottom_left: np.ndarray  # shape: (3,)
    bottom_right: np.ndarray  # shape: (3,)


class TV:
    """
    A class representing a TV with support for multiple camera coordinate systems.
    The TV's local coordinate system is defined as:
    - Origin: Top-left corner of the TV
    - X-axis: From top-left to top-right
    - Y-axis: From top-left to bottom-left
    - Z-axis: Normal to the TV surface (X × Y)
    """

    def __init__(
        self,
        physical_dimensions: Tuple[float, float],  # (width, height) in mm
        resolution: Tuple[int, int],  # (width, height) in pixels
    ):
        """
        Initialize the TV with its physical properties.
        The TV's local coordinate system is centered at its top-left corner.

        Args:
            physical_dimensions: Physical width and height of TV in mm
            resolution: Screen resolution in pixels
        """
        # Validate inputs
        if not all(d > 0 for d in physical_dimensions):
            raise ValueError("Physical dimensions must be positive")
        if not all(r > 0 for r in resolution):
            raise ValueError("Resolution must be positive")

        self.physical_dimensions = np.array(physical_dimensions, dtype=np.float64)
        self.resolution = np.array(resolution, dtype=np.int32)

        # Initialize local coordinate system at top-left corner
        self.local_corners = self._initialize_local_corners()

        # Initialize camera-related attributes
        self.camera_corners: Dict[str, TVCorners] = {}
        self.camera_planes: Dict[str, Plane3D] = {}
        self.camera_transforms: Dict[str, Dict[str, np.ndarray]] = {}

        logger.info(
            f"Initialized TV with dimensions {physical_dimensions}mm and resolution {resolution}"
        )

    def _initialize_local_corners(self) -> TVCorners:
        """Initialize TV corners in local coordinate system"""
        return TVCorners(
            top_left=np.array([0, 0, 0], dtype=np.float64),
            top_right=np.array([self.physical_dimensions[0], 0, 0], dtype=np.float64),
            bottom_left=np.array([0, self.physical_dimensions[1], 0], dtype=np.float64),
            bottom_right=np.array(
                [self.physical_dimensions[0], self.physical_dimensions[1], 0],
                dtype=np.float64,
            ),
        )

    def add_camera(
        self,
        camera_id: str,
        rvec: np.ndarray,  # Rotation vector
        tvec: np.ndarray,  # Translation vector
    ) -> None:
        """
        Add a camera to the TV's coordinate system.

        Args:
            camera_id: Unique identifier for the camera
            rvec: 3x1 rotation vector
            tvec: 3x1 translation vector

        Raises:
            ValueError: If camera_id already exists or transformation vectors are invalid
        """
        # Validate inputs
        if camera_id in self.camera_corners:
            raise ValueError(f"Camera {camera_id} already exists")
        if rvec.shape != (3, 1) or tvec.shape != (3, 1):
            raise ValueError("Rotation and translation vectors must be 3x1")
        if np.any(np.isnan(rvec)) or np.any(np.isnan(tvec)):
            raise ValueError("Transformation vectors cannot contain NaN values")
        if np.any(np.isinf(rvec)) or np.any(np.isinf(tvec)):
            raise ValueError("Transformation vectors cannot contain infinite values")

        # Ensure vectors are in float64 format for OpenCV
        rvec = np.asarray(rvec, dtype=np.float64)
        tvec = np.asarray(tvec, dtype=np.float64)

        # Handle very small rotation vectors
        rvec_magnitude = np.linalg.norm(rvec)
        if rvec_magnitude < 1e-10:
            # For very small rotations, use identity matrix
            R = np.eye(3, dtype=np.float64)
        else:
            try:
                R, _ = cv2.Rodrigues(rvec)
            except cv2.error as e:
                raise ValueError(f"Invalid rotation vector: {str(e)}")

        # Store camera transformation
        self.camera_transforms[camera_id] = {
            "R": R,
            "tvec": tvec,
            "rvec": rvec,
        }

        # Transform TV corners to camera space
        corners = self._transform_corners_to_camera_space(camera_id)
        self.camera_corners[camera_id] = corners

        # Calculate and store plane equation
        self.camera_planes[camera_id] = self._calculate_plane_equation(corners)

        logger.info(f"Added camera {camera_id} with transformation R=\n{R}, tvec=\n{tvec}")

    def remove_camera(self, camera_id: str) -> None:
        """
        Remove a camera from the TV's coordinate system.

        Args:
            camera_id: Identifier of the camera to remove

        Raises:
            KeyError: If camera_id doesn't exist
        """
        if camera_id not in self.camera_corners:
            raise KeyError(f"Camera {camera_id} not found")

        del self.camera_corners[camera_id]
        del self.camera_planes[camera_id]
        del self.camera_transforms[camera_id]

        logger.info(f"Removed camera {camera_id}")

    def get_camera_info(self, camera_id: str) -> Dict[str, np.ndarray]:
        """
        Get camera transformation information.

        Args:
            camera_id: Camera identifier

        Returns:
            Dictionary containing camera transformation information

        Raises:
            KeyError: If camera_id doesn't exist
        """
        if camera_id not in self.camera_transforms:
            raise KeyError(f"Camera {camera_id} not found")

        return self.camera_transforms[camera_id]

    def _transform_corners_to_camera_space(self, camera_id: str) -> TVCorners:
        """Transform TV corners from local to camera space"""
        R = self.camera_transforms[camera_id]["R"]

        # if t is not flatten, then it have (3,1) shape which will cause error while appling matrix multiplication + broadcasting
        # Just because of this line, my normal which was supposed to a,b,c,d as scaler, they become a,b,c become (3x1) vector each
        # and d became (3x3)
        t = self.camera_transforms[camera_id]["tvec"].flatten()

        corners = self.local_corners
        return TVCorners(
            top_left=R @ corners.top_left + t,
            top_right=R @ corners.top_right + t,
            bottom_left=R @ corners.bottom_left + t,
            bottom_right=R @ corners.bottom_right + t,
        )

    def _calculate_plane_equation(self, corners: TVCorners) -> Plane3D:
        """Calculate plane equation from TV corners"""
        # Calculate two vectors in the plane
        v1 = corners.top_right - corners.top_left
        v2 = corners.bottom_left - corners.top_left

        # Calculate normal vector
        normal = np.cross(v1, v2)
        normal = normal / np.linalg.norm(normal)

        # Calculate d parameter
        d = -np.dot(normal, corners.top_left)

        return Plane3D(a=normal[0], b=normal[1], c=normal[2], d=d)

    def ray_plane_intersection(
        self,
        camera_id: str,
        ray_origin: np.ndarray,
        ray_direction: np.ndarray,
        epsilon: float = 1e-10,
    ) -> Optional[Tuple[float, float]]:
        """
        Calculate intersection of a ray with the TV plane in camera space.

        Args:
            camera_id: Camera identifier
            ray_origin: 3x1 array representing ray origin
            ray_direction: 3x1 array representing ray direction
            epsilon: Small value for numerical stability

        Returns:
            Tuple of (u, v) pixel coordinates if intersection exists, None otherwise

        Raises:
            KeyError: If camera_id doesn't exist
            ValueError: If ray parameters are invalid
        """
        if camera_id not in self.camera_planes:
            raise KeyError(f"Camera {camera_id} not found")
        if ray_origin.shape != (3, 1) or ray_direction.shape != (3, 1):
            raise ValueError("Ray origin and direction must be 3x1 arrays")
        if np.any(np.isnan(ray_origin)) or np.any(np.isnan(ray_direction)):
            raise ValueError("Ray parameters cannot contain NaN values")

        ray_origin = ray_origin.flatten()
        ray_direction = ray_direction.flatten()

        # Get plane equation and normal

        # seems some problem with the plane.

        plane = self.camera_planes[camera_id]
        # print(f" Plane is : {plane}")
        normal = np.array([plane.a, plane.b, plane.c])
        normal_magnitude = np.linalg.norm(normal)

        # Check for zero normal vector
        # DEBUG: 1
        # print(f"DEBUG:1 normal_magnitude: {type(normal_magnitude)}")
        if normal_magnitude < epsilon:
            raise ValueError("Invalid plane normal vector")

        # Normalize ray direction with numerical stability
        ray_magnitude = np.linalg.norm(ray_direction)
        # print(f"DEBUG:2 ray_magnitude: {type(ray_magnitude)}")
        if ray_magnitude < epsilon:
            raise ValueError("Ray direction vector too small")
        ray_direction = ray_direction / ray_magnitude

        # Calculate scale-aware epsilon
        # Consider magnitudes of normal, ray direction, and ray origin
        scale = max(normal_magnitude, ray_magnitude, np.linalg.norm(ray_origin))
        rel_epsilon = epsilon * scale

        # Check if ray is parallel to plane
        # Use dot product of normalized vectors for better numerical stability
        # print(f" normal shape: {normal.shape}")
        denom = np.dot(normal / normal_magnitude, ray_direction)

        # Special handling for rays perpendicular to plane
        # print(f"DEBUG:3 demon: {type(denom)}")
        if abs(denom) < rel_epsilon:
            # Check if ray origin is on the plane
            dist_to_plane = abs(
                np.dot(normal / normal_magnitude, ray_origin)
                + plane.d / normal_magnitude
            )
            # print(f"DEBUG:4 dist_to_plane: {type(dist_to_plane)}")
            if dist_to_plane < rel_epsilon:
                # Ray is on the plane, use ray origin as intersection
                intersection = ray_origin
            else:
                return None

        # Calculate intersection parameter with numerical stability
        # Use normalized normal vector for better precision
        numerator = -(
            np.dot(normal / normal_magnitude, ray_origin) + plane.d / normal_magnitude
        )
        t = numerator / denom

        # Check if ray is pointing away from the plane
        # This is more robust than just checking t < 0
        # print(f"DEBUG:5 normal product: {type(np.dot(normal, ray_direction))}")
        if np.dot(normal, ray_direction) > 0:
            return None

        # Calculate intersection point
        intersection = ray_origin + t * ray_direction

        # Get camera transformation
        R = self.camera_transforms[camera_id]["R"]
        t = self.camera_transforms[camera_id]["tvec"].flatten()

        # Transform intersection point to TV's local coordinate system
        # print(f"Dimension test: intersection shape: {intersection.shape}")
        # print(f"Dimension test: t shape: {t.shape}")
        local_point = R.T @ (intersection - t)

        # Check if point is within TV bounds with numerical stability
        # Use relative epsilon based on TV dimensions
        tv_scale = max(self.physical_dimensions)
        tv_epsilon = epsilon * tv_scale
        # print(f"DEBUG:6 tv_epsilon: {type(tv_epsilon)}")
        # print(f"DEBUG:6 tv_scale: {type(tv_scale)}")
        # print(f"DEBUG:6 local_point: {type(local_point[0])}")
        # print(f"DEBUG:6 local_point_shape: {local_point.shape}")
        # DEBUG: 6
        # print("DEBUG: 6 Before TV corner test")
        if not (
            -tv_epsilon <= local_point[0] <= self.physical_dimensions[0] + tv_epsilon
            and -tv_epsilon
            <= local_point[1]
            <= self.physical_dimensions[1] + tv_epsilon
        ):
            return None

        # print("DEBUG: 7 After TV corner test")
        # Convert to pixel coordinateqs
        u = local_point[0] * self.resolution[0] / self.physical_dimensions[0]
        v = local_point[1] * self.resolution[1] / self.physical_dimensions[1]

        # Ensure pixel coordinates are within bounds
        # print("DEBUG: 8 Before pixel bounds test")
        if not (0 <= u < self.resolution[0] and 0 <= v < self.resolution[1]):
            return None
        # print("DEBUG: 9 After pixel bounds test")
        return u, v

    def pixel_to_world(
        self, camera_id: str, pixel_coords: Tuple[float, float]
    ) -> Optional[np.ndarray]:
        """
        Convert pixel coordinates to 3D world coordinates.

        Args:
            camera_id: Camera identifier
            pixel_coords: Tuple of (u, v) pixel coordinates

        Returns:
            3x1 array of world coordinates if valid, None otherwise

        Raises:
            KeyError: If camera_id doesn't exist
            ValueError: If pixel coordinates are invalid
        """
        if camera_id not in self.camera_corners:
            raise KeyError(f"Camera {camera_id} not found")
        if not (
            0 <= pixel_coords[0] < self.resolution[0]
            and 0 <= pixel_coords[1] < self.resolution[1]
        ):
            raise ValueError("Pixel coordinates out of bounds")

        # Convert pixel coordinates to local coordinates
        local_x = pixel_coords[0] * self.physical_dimensions[0] / self.resolution[0]
        local_y = pixel_coords[1] * self.physical_dimensions[1] / self.resolution[1]
        local_point = np.array([local_x, local_y, 0])

        # Transform to camera space
        R = self.camera_transforms[camera_id]["R"]
        t = self.camera_transforms[camera_id]["tvec"]
        world_point = R @ local_point + t

        return world_point

    def world_to_pixel(
        self, camera_id: str, world_point: np.ndarray
    ) -> Optional[Tuple[float, float]]:
        """
        Convert 3D world coordinates to pixel coordinates.

        Args:
            camera_id: Camera identifier
            world_point: 3x1 array of world coordinates

        Returns:
            Tuple of (u, v) pixel coordinates if valid, None otherwise

        Raises:
            KeyError: If camera_id doesn't exist
            ValueError: If world coordinates are invalid
        """
        if camera_id not in self.camera_corners:
            raise KeyError(f"Camera {camera_id} not found")
        if world_point.shape != (3,):
            raise ValueError("World point must be a 3x1 array")

        # Get camera transformation
        R = self.camera_transforms[camera_id]["R"]
        t = self.camera_transforms[camera_id]["tvec"]

        # Transform to local coordinates using inverse transformation
        # For rigid body transformation, inverse is:
        # - Rotation: R^T
        # - Translation: -R^T @ t
        local_point = R.T @ (world_point - t)

        # Check if point is within TV bounds
        if not (
            0 <= local_point[0] <= self.physical_dimensions[0]
            and 0 <= local_point[1] <= self.physical_dimensions[1]
        ):
            return None

        # Convert to pixel coordinates
        u = local_point[0] * self.resolution[0] / self.physical_dimensions[0]
        v = local_point[1] * self.resolution[1] / self.physical_dimensions[1]

        return u, v

    def is_valid(self) -> bool:
        """
        Check if TV state is valid.

        Returns:
            True if TV state is valid, False otherwise
        """
        try:
            # Check physical dimensions and resolution
            if not all(d > 0 for d in self.physical_dimensions):
                return False
            if not all(r > 0 for r in self.resolution):
                return False

            # Check local corners
            if not all(
                corner.shape == (3,)
                for corner in [
                    self.local_corners.top_left,
                    self.local_corners.top_right,
                    self.local_corners.bottom_left,
                    self.local_corners.bottom_right,
                ]
            ):
                return False

            # Check camera data consistency
            for camera_id in self.camera_corners:
                if (
                    camera_id not in self.camera_planes
                    or camera_id not in self.camera_transforms
                ):
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating TV state: {str(e)}")
            return False

    def get_state(self) -> Dict[str, Any]:
        """
        Get TV state for serialization.

        Returns:
            Dictionary containing TV state
        """
        return {
            "physical_dimensions": self.physical_dimensions.tolist(),
            "resolution": self.resolution.tolist(),
            "camera_transforms": {
                camera_id: {
                    "R": transform["R"].tolist(),
                    "tvec": transform["tvec"].tolist(),
                }
                for camera_id, transform in self.camera_transforms.items()
            },
        }

    @classmethod
    def from_state(cls, state: Dict[str, Any]) -> "TV":
        """
        Create TV instance from serialized state.

        Args:
            state: Dictionary containing TV state

        Returns:
            TV instance

        Raises:
            ValueError: If state is invalid
        """
        try:
            tv = cls(
                physical_dimensions=tuple(state["physical_dimensions"]),
                resolution=tuple(state["resolution"]),
            )

            for camera_id, transform in state["camera_transforms"].items():
                R = np.array(transform["R"], dtype=np.float64)
                tvec = np.array(transform["tvec"], dtype=np.float64)
                rvec, _ = cv2.Rodrigues(R)
                tv.add_camera(camera_id, rvec, tvec)

            return tv

        except Exception as e:
            raise ValueError(f"Invalid TV state: {str(e)}")

    def save_to_file(self, filepath: str) -> None:
        """
        Save TV state to file.

        Args:
            filepath: Path to save TV state

        Raises:
            IOError: If file cannot be written
        """
        try:
            state = self.get_state()
            with open(filepath, "w") as f:
                json.dump(state, f, indent=2)
            logger.info(f"Saved TV state to {filepath}")
        except Exception as e:
            raise IOError(f"Error saving TV state: {str(e)}")

    @classmethod
    def load_from_file(cls, filepath: str) -> "TV":
        """
        Load TV instance from file.

        Args:
            filepath: Path to TV state file

        Returns:
            TV instance

        Raises:
            IOError: If file cannot be read
            ValueError: If state is invalid
        """
        try:
            with open(filepath, "r") as f:
                state = json.load(f)
            return cls.from_state(state)
        except Exception as e:
            raise IOError(f"Error loading TV state: {str(e)}")