# === Standard Libraries ===
import os
from typing import Tuple

# === Third-Party Libraries ===
import cv2
import torch
import numpy as np
import pandas as pd
import albumentations as A
from albumentations.pytorch import ToTensorV2
from torch.utils.data import Dataset, DataLoader

# === Local Modules ===
from utils import util


class CustomGazeDataset(Dataset):
    """
    PyTorch Dataset for normalized gaze tracking data.

    This dataset loads face images along with corresponding gaze labels
    from a structured directory format.

    Expected directory structure:
        <data_root>/
        ├── labels.csv
        └── pXX/
            └── <basename>-face.jpg

    CSV format:
        - face_file_name: Relative path to face image
        - pitch: Gaze pitch angle in radians
        - yaw: Gaze yaw angle in radians

    Args:
        data_path: Path to the data directory containing labels.csv and image folders
        transform: Albumentations transform pipeline to apply to images
    """
    def __init__(self,
                 data_path: str,
                 transform=None,
                 pitch_range: Tuple[int, int] = (-35, 0), # (-25, 25)
                 yaw_range:   Tuple[int, int] = (-35, 35), # (-35, 35)
        ):
        self.data_path = data_path
        self.transform = transform
        self.df = pd.read_csv(f"{data_path}/labels.csv")

        # Filter out samples with angles outside the desired range
        pitch_deg = np.degrees(self.df['pitch'])
        yaw_deg = np.degrees(self.df['yaw'])
        
        # Filter based on pitch and yaw ranges
        valid_indices = (
            (pitch_deg >= pitch_range[0]) &
            (pitch_deg <= pitch_range[1]) &
            (yaw_deg >= yaw_range[0]) &
            (yaw_deg <= yaw_range[1])
        )
        orig_list_len = len(self.df)
        self.df = self.df[valid_indices].reset_index(drop=True)

        removed_items = orig_list_len - len(self.df)
        print(f"{removed_items} items removed from dataset that not in ranges:")
        print(f"P: ({pitch_range[0]}, {pitch_range[1]}) and Y: ({yaw_range[0]}, {yaw_range[1]})")
        print(f"Pitch | min-avg-max: [{pitch_deg.min():.1f}, {pitch_deg.mean():.1f}, {pitch_deg.max():.1f}]")
        print(f"Yaw   | min-avg-max: [{yaw_deg.min():.1f}, {yaw_deg.mean():.1f}, {yaw_deg.max():.1f}]\n")
        
    def __len__(self):
        return len(self.df)

    def __getitem__(self, idx):
        row = self.df.iloc[idx]

        # Get info
        grid_cell = np.array(row.grid_cell, dtype=np.int32)
        point_on_screen = np.array([row.point_on_screen_x, row.point_on_screen_y], dtype=np.int32)

        # Get face center coordinates in 3D
        face_center = np.array([row.face_center_x, row.face_center_y, row.face_center_z], dtype=np.float32)

        # Get gaze vector
        gaze_vector = np.array([row.gaze_vector_x, row.gaze_vector_y, row.gaze_vector_z], dtype=np.float32)
        
        # Get normalized gaze vector
        normalized_gaze_vector = np.array([
            row.normalized_gaze_vector_x,
            row.normalized_gaze_vector_y,
            row.normalized_gaze_vector_z
        ], dtype=np.float32)
        
        # Get invSR_matrix from normalization matrices
        normalization_matrices = np.load(f"{self.data_path}/{row.matrices_file}")
        R = normalization_matrices["R"]
        S = normalization_matrices["S"]
        invR_matrix = np.linalg.inv(R)
        invSR_matrix = np.linalg.inv(np.dot(S, R))
        
        # Get face file path
        p, file_name = row.face_file_name.split("/")
        
        # Load face images
        original_face = cv2.imread(f"{self.data_path}/{p}/original/{file_name}")
        normalized_face = cv2.imread(f"{self.data_path}/{p}/{file_name}")
        
        # Get gaze vector
        gaze = (row.pitch, row.yaw)
        
        # Apply transforms
        image = cv2.cvtColor(normalized_face, cv2.COLOR_BGR2RGB)
        image = util.clahe(image)
        image = self.transform(image=image)["image"]

        # Get labels
        label = torch.stack([torch.tensor(gaze[0], dtype=torch.float32),
                             torch.tensor(gaze[1], dtype=torch.float32)], dim=0)
        
        info = {
            "original_face": original_face,
            "normalized_face": normalized_face,
            "grid_cell": grid_cell,
            "point_on_screen": point_on_screen,
            "face_center": face_center,
            "gaze_vector": gaze_vector,
            "normalized_gaze_vector": normalized_gaze_vector,
            "invR_matrix": invR_matrix,
            "invSR_matrix": invSR_matrix,
        }
        return image, label, info


def get_dataloaders(args, val_split=0.1) -> tuple:
    """
    Create train, validation, and test dataloaders for gaze tracking.

    This function automatically splits the training data into train/validation sets
    and creates separate dataloaders with appropriate transforms. Training data
    uses augmentation while validation and test data use only normalization.

    Args:
        - data_root: Root directory containing 'train' and 'test' subdirectories
        - batch_size: Batch size for all dataloaders
        - num_workers: Number of worker processes for data loading

    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    data_root = args.data
    batch_size = 1
    num_workers = 0

    train_data_path = os.path.join(data_root, 'train')
    test_data_path = os.path.join(data_root, 'test')

    # Create val, test transforms
    transform_train = transform_val = transform_test = A.Compose([
        A.Normalize(),
        ToTensorV2()
    ], seed=42)
    
    # Create the full training dataset first to get the correct filtered length
    full_train_dataset = CustomGazeDataset(train_data_path, transform_train)
    full_val_dataset = CustomGazeDataset(train_data_path, transform_val)
    test_dataset = CustomGazeDataset(test_data_path, transform_test)

    # Split dataset based on the filtered dataset length
    n = len(full_train_dataset)
    indices = list(range(n))
    np.random.shuffle(indices)
    val_size = int(n * val_split)

    val_idx = indices[:val_size]
    train_idx = indices[val_size:]

    # Create three datasets with different transforms
    train_dataset = torch.utils.data.Subset(full_train_dataset, train_idx)
    val_dataset = torch.utils.data.Subset(full_val_dataset, val_idx)

    train_loader = DataLoader(train_dataset,
                              batch_size=batch_size,
                              shuffle=True,
                              num_workers=num_workers,
                              pin_memory=True,
                              worker_init_fn=util.seed_worker,
                              generator=util.g)
    val_loader = DataLoader(val_dataset,
                            batch_size=batch_size,
                            shuffle=False,
                            num_workers=num_workers,
                            pin_memory=True,
                            worker_init_fn=util.seed_worker,
                            generator=util.g)
    test_loader = DataLoader(test_dataset,
                             batch_size=batch_size,
                             shuffle=False,
                             num_workers=num_workers,
                             pin_memory=True,
                             worker_init_fn=util.seed_worker,
                             generator=util.g)

    return train_loader, val_loader, test_loader
