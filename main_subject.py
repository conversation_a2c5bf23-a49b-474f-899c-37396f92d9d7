# === Standard Libraries ===
import logging
from argparse import ArgumentParser

# === Third-Party Libraries ===
import cv2
import torch
import numpy as np
from tqdm import tqdm
import screeninfo as si

# === Local Modules ===
from utils.util import *
from utils.tv3D import TV
from utils.datasets_subject import get_dataloaders
from nets.vgg_face_subject import FinalModel


device = torch.device("cuda" if torch.cuda.is_available() else "cpu")


@torch.no_grad()
def main(args):
    # import torchvision.models as models
    # model = models.get_model(name="regnet_x_800mf", weights=models.RegNet_X_800MF_Weights.DEFAULT)
    # model.fc = torch.nn.Linear(model.fc.in_features, 2)
    
    # # Initialize the regression layer
    # torch.nn.init.normal_(model.fc.weight, std=0.001)
    # if model.fc.bias is not None:
    #     torch.nn.init.constant_(model.fc.bias, 0)
        
    # Load the model
    model = FinalModel()
    model.load_state_dict(torch.load(args.model_path, map_location=device, weights_only=True))
    model.to(device)
    model.eval()

    # Get the monitors info using screeninfo package
    monitors = si.get_monitors()
    if len(monitors) < 1:
        raise ValueError("No TV is connected to the laptop based setup.")
    print("Monitors:", monitors)

    freeze = args.freeze
    
    screen = monitors[0]
    monitor_mm = (screen.width_mm, screen.height_mm)
    monitor_px = (screen.width, screen.height)

    # Create the screen canvas and heatmap
    screen_canvas, heatmap = create_screen_canvas(screen)
    
    s = 96
    # Normalized Face
    winname_cropped_face = "Cropped Face"
    cv2.namedWindow(winname_cropped_face, cv2.WINDOW_NORMAL)
    cv2.moveWindow(winname_cropped_face, (screen.width//4) - s, screen.y)
    cv2.resizeWindow(winname_cropped_face, s, int(s*1.5))
    
    # Normalized Face
    winname_normalized_face = "Normalized Face"
    cv2.namedWindow(winname_normalized_face, cv2.WINDOW_NORMAL)
    cv2.moveWindow(winname_normalized_face, (screen.width//4), screen.y)
    cv2.resizeWindow(winname_normalized_face, s, int(s*1.5))

    # TV
    winname_tv = "TV"
    cv2.namedWindow(winname_tv, cv2.WND_PROP_FULLSCREEN)
    cv2.setWindowProperty(winname_tv, cv2.WND_PROP_FULLSCREEN, cv2.WINDOW_FULLSCREEN)

    # Load the TV calibration data
    data = read_congfig_file(args.tv_calib_path)
    rvec_screen_to_cam = cv2.Rodrigues(np.asarray(data['Rmat_screen_to_cam']).reshape(3, 3))[0]
    tvec_screen_to_cam = np.asarray(data['tvec_screen_to_cam']).reshape(3, 1)

    # Initialize the TV class for 3D task
    camera_id = str(0)
    tv1 = TV(monitor_mm, monitor_px)
    tv1.add_camera(camera_id=camera_id, rvec=rvec_screen_to_cam, tvec=tvec_screen_to_cam)

    # Load dataloaders with configurable augmentation
    train_loader, val_loader, test_loader = get_dataloaders(args)
    data_loader = test_loader
    
    sum_angular_error = 0
    total_num_samples = 0
    inv_sum_angular_error = 0
    num_batches = len(data_loader)
    
    for images, labels, info in tqdm(data_loader, total=num_batches):
        # Get info
        original_face = info["original_face"].squeeze(0).detach().cpu().numpy()
        normalized_face = info["normalized_face"].squeeze(0).detach().cpu().numpy()
        grid_cell = info["grid_cell"].squeeze(0).detach().cpu().numpy()
        point_on_screen = info["point_on_screen"].squeeze(0).detach().cpu().numpy()
        face_center = info["face_center"].squeeze(0).detach().cpu().numpy()
        gaze_vector = info["gaze_vector"].squeeze(0).detach().cpu().numpy()
        normalized_gaze_vector = info["normalized_gaze_vector"].squeeze(0).detach().cpu().numpy()
        invR_matrix = info["invR_matrix"].squeeze(0).detach().cpu().numpy()
        invSR_matrix = info["invSR_matrix"].squeeze(0).detach().cpu().numpy()
        
        # Get the inverse matrix for denormalizing
        if args.scaled == True:
            inv_matrix = invSR_matrix
        else:
            inv_matrix = invR_matrix
        
        num_samples = labels.size(0)
        # ===============================================================
        # Move to device
        images = images.to(device)
        labels = labels.to(device)
        torch_inv_matrix = torch.from_numpy(inv_matrix).float().to(device)
        
        # Forward pass
        outputs = model(images)

        # Calculate losses with configurable weights and safety checks
        angular_error = calculate_gaze_angle_error(labels, outputs)[1]
        inv_angular_error = calculate_gaze_angle_error(labels, outputs, torch_inv_matrix)[1]

        # Average loss and angular error regarding batch size
        sum_angular_error += angular_error * num_samples
        inv_sum_angular_error += inv_angular_error * num_samples
        
        # Total number of samples
        total_num_samples += num_samples
        # ===============================================================
        
        # verify the denormalization
        denormalized_gaze_vector = np.dot(inv_matrix, normalized_gaze_vector)
        denormalized_gaze_vector = normalize(denormalized_gaze_vector)
        is_almost_same(gaze_vector, denormalized_gaze_vector, as_vector=True)
        
        # Get the output
        output = outputs.squeeze(0).detach().cpu().numpy()

        # Convert pitch and yaw to 3D vector
        pred_normalized_gaze_vector = pitchyaw_to_3d_vector_numpy(output)

        # For visualization
        to_show_pred_normalized_gaze_vector = pred_normalized_gaze_vector.copy()

        # Transform the gaze vector to camera coordinate system
        pred_denormalized_gaze_vector = np.dot(inv_matrix, pred_normalized_gaze_vector)
        pred_denormalized_gaze_vector = normalize(pred_denormalized_gaze_vector)

        # For visualization
        to_show_pred_denormalized_gaze_vector = pred_denormalized_gaze_vector.copy()
        
        # Step 2: Gaze/TV intersection section
        face_center = face_center.reshape(3, 1)
        pred_denormalized_gaze_vector = pred_denormalized_gaze_vector.reshape(3, 1)
        
        # Flip both if pointing backward
        if pred_denormalized_gaze_vector[2, 0] < 0:
            pred_denormalized_gaze_vector *= -1

        # Compute intersection of gaze vector with screen plane
        pred_px = tv1.ray_plane_intersection(camera_id, face_center, pred_denormalized_gaze_vector)

        # Draw on canvas
        screen_canvas_heatmap = screen_canvas.copy()

        # Apply Gaussian kernel heatmap
        if pred_px is not None:
            pred_px = tuple(map(int, pred_px))

            # Gaussian parameters
            radius = 150
            sigma = radius / 3

            # Ensure pred_px is within bounds
            x, y = int(pred_px[0]), int(pred_px[1])
            x_min = max(x - radius, 0)
            x_max = min(x + radius, heatmap.shape[1])
            y_min = max(y - radius, 0)
            y_max = min(y + radius, heatmap.shape[0])

            # Patch dimensions
            patch_w = x_max - x_min
            patch_h = y_max - y_min

            # Generate patch grid
            xv, yv = np.meshgrid(np.arange(patch_w), np.arange(patch_h))
            xv = xv - (x - x_min)
            yv = yv - (y - y_min)

            # Apply 2D Gaussian
            gaussian_patch = np.exp(-(xv**2 + yv**2) / (2 * sigma**2))
            gaussian_patch = np.clip(gaussian_patch, 0, 1)

            # Add to heatmap with clipping
            heatmap[y_min:y_max, x_min:x_max] = np.clip(
                heatmap[y_min:y_max, x_min:x_max] + gaussian_patch, 0, 1
            )

            # Optional: fade previous gaze heat over time
            heatmap *= 0.65

            # Convert and blend
            heatmap_uint8 = (heatmap * 255).astype(np.uint8)
            heatmap_color = cv2.applyColorMap(heatmap_uint8, cv2.COLORMAP_JET)

            # Blend the heatmap with the screen canvas
            alpha = 0.4
            beta = 1 - alpha
            gamma = 0
            screen_canvas_heatmap = cv2.addWeighted(heatmap_color, alpha, screen_canvas_heatmap, beta, gamma)
        
        # Draw the difference between the ground truth and predicted gaze
        draw_difference_between_gaze(
            screen_canvas_heatmap,
            point_on_screen,
            pred_px,
            angular_error,
            inv_angular_error
        )
            
        # Draw the gaze arrow on orignal face
        # TODO Draw a gaze arrow on cropped face by true pinhole projection (x, y, z),  currently (x, y)
        draw_gaze_arrow_from_vector(original_face, to_show_pred_denormalized_gaze_vector, color=(0, 100, 255), thickness=2, length=50)
        draw_gaze_arrow_from_vector(original_face, gaze_vector, color=(0, 255, 0), thickness=2, length=50)
        original_face_flipped = cv2.flip(original_face, 1)
        
        # Draw the gaze arrow on normalized face
        draw_gaze_arrow_from_vector(normalized_face, to_show_pred_normalized_gaze_vector, color=(0, 100, 255), thickness=2, length=50)
        draw_gaze_arrow_from_vector(normalized_face, normalized_gaze_vector, color=(0, 255, 0), thickness=2, length=50)
        normalized_face_flipped = cv2.flip(normalized_face, 1)
        
        # Draw the pitch and yaw angles
        if args.show_pitch_yaw:
            draw_pitch_yaw_from_vector(original_face_flipped, to_show_pred_denormalized_gaze_vector, label="PR")
            draw_pitch_yaw_from_vector(original_face_flipped, gaze_vector, label="GT")
            draw_pitch_yaw_from_vector(normalized_face_flipped, to_show_pred_normalized_gaze_vector, label="PR")
            draw_pitch_yaw_from_vector(normalized_face_flipped, normalized_gaze_vector, label="GT")
        
        # Show frame and heatmap
        cv2.imshow(winname_cropped_face, original_face_flipped)
        cv2.imshow(winname_normalized_face, normalized_face_flipped)
        cv2.imshow(winname_tv, screen_canvas_heatmap)

        # Get a key
        key = cv2.waitKey(freeze) & 0xFF

        if key == ord('q'):   # Move to next image
            break
        elif key == ord('f'):   # Toggle freeze mode
            freeze = 0 if freeze else 1
                    
    # Average loss and angular error regarding batch size
    avg_angular_error = sum_angular_error / total_num_samples
    avg_inv_angular_error = inv_sum_angular_error / total_num_samples
    
    logging.info(
        f"\nTotal Number of Samples: {total_num_samples} |"
        f"\nAngular Vector Error: {avg_angular_error:.7f}° |"
        f"\nInversed Angular Vector Error: {avg_inv_angular_error:.7f}°"
    )

    cv2.destroyAllWindows()


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument("--data", type=str, default="data/dataset_normalized_merged_v1-v2-v3-v4_SCALE_FDN411-600_5")
    # parser.add_argument("--model_path", type=str, default="weights/subject_best_scaled_FDN411-600_v4_regnetx800_exp_189.pt")
    parser.add_argument("--model_path", type=str, default="weights/subject_best_scaled_FDN411-600_v4.pt")
    parser.add_argument("--tv_calib_path", type=str, default="configs/TV_calibration.yaml")
    parser.add_argument("--scaled", type=bool, default=True)
    parser.add_argument("--show_pitch_yaw", type=bool, default=True)
    parser.add_argument('--freeze', default=0, type=int, help="Freeze mode (0=continuous, 1=wait for key)")
    args = parser.parse_args()

    main(args)
